# Luminari MUD Wilderness System Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Coordinate System](#coordinate-system)
4. [Terrain Generation](#terrain-generation)
5. [Room Management](#room-management)
6. [Regions and Paths](#regions-and-paths)
7. [Weather System](#weather-system)
8. [Player Experience](#player-experience)
9. [Builder Tools](#builder-tools)
10. [Technical Implementation](#technical-implementation)
11. [Configuration](#configuration)
12. [Troubleshooting](#troubleshooting)

## Overview

The Luminari MUD Wilderness System is a sophisticated procedural terrain generation and management system that creates vast, explorable wilderness areas. The system combines:

- **Procedural Generation**: Uses Perlin noise algorithms to generate realistic terrain
- **Dynamic Room Creation**: Creates rooms on-demand as players explore
- **Static Room Support**: Allows builders to create permanent wilderness locations
- **Region System**: Defines special areas with unique properties
- **Path System**: Creates roads, rivers, and other linear features
- **Weather Integration**: Location-specific weather patterns
- **Coordinate-Based Navigation**: X,Y coordinate system for precise positioning

### Key Features

- **Infinite Exploration**: 2048x2048 coordinate grid (4+ million possible locations)
- **Realistic Terrain**: Elevation, moisture, and temperature-based biome generation
- **Performance Optimized**: KD-Tree indexing and dynamic room pooling
- **Builder Friendly**: OLC integration and buildwalk support
- **Player Immersive**: Integrated mapping, weather, and navigation

## System Architecture

### Core Components

```
wilderness.c/h     - Main wilderness engine
perlin.c/h         - Noise generation algorithms
mysql.c/h          - Database integration for regions/paths
weather.c          - Weather system integration
act.movement.c     - Movement handling
redit.c            - OLC wilderness room editing
```

### Data Flow

```
Player Movement → Coordinate Calculation → Room Lookup/Creation → 
Terrain Generation → Region/Path Application → Room Assignment → 
Weather/Description Generation → Player Display
```

## Coordinate System

### Coordinate Space
- **Range**: -1024 to +1024 on both X and Y axes
- **Origin**: (0,0) at the center of the wilderness
- **Directions**: 
  - North: +Y
  - South: -Y  
  - East: +X
  - West: -X

### Room VNUM Allocation

```c
#define WILD_ROOM_VNUM_START 1000000        // Static wilderness rooms
#define WILD_ROOM_VNUM_END 1003999          // End of static range
#define WILD_DYNAMIC_ROOM_VNUM_START 1004000 // Dynamic room pool
#define WILD_DYNAMIC_ROOM_VNUM_END 1009999   // End of dynamic range
```

### Coordinate Storage
Each wilderness room stores its coordinates in `world[room].coords[]`:
- `coords[0]` = X coordinate
- `coords[1]` = Y coordinate

## Terrain Generation

### Noise Layers
The system uses multiple Perlin noise layers:

1. **Elevation** (`NOISE_MATERIAL_PLANE_ELEV`)
   - Seed: 822344
   - Creates mountains, hills, valleys
   - Ridged multifractal for realistic mountain ranges

2. **Moisture** (`NOISE_MATERIAL_PLANE_MOISTURE`) 
   - Seed: 834
   - Determines wetness/dryness
   - Affects vegetation and water features

3. **Temperature** (derived from elevation)
   - Higher elevation = colder temperature
   - Affects biome selection

4. **Weather** (`NOISE_WEATHER`)
   - Seed: 43425
   - Local weather pattern variations

### Sector Type Determination

The system combines elevation, temperature, and moisture to determine terrain types:

```c
int get_sector_type(int elevation, int temperature, int moisture)
```

**Sector Types Include:**
- `SECT_FIELD` - Grasslands and plains
- `SECT_FOREST` - Wooded areas
- `SECT_HILLS` - Rolling hills
- `SECT_MOUNTAIN` - Mountain peaks
- `SECT_HIGH_MOUNTAIN` - Impassable peaks
- `SECT_WATER_SWIM` - Lakes and ponds
- `SECT_WATER_NOSWIM` - Deep water
- `SECT_RIVER` - Flowing water
- `SECT_BEACH` - Coastal areas
- `SECT_DESERT` - Arid regions
- `SECT_TUNDRA` - Cold plains

### Terrain Visualization

Each sector type has associated map symbols and colors:

```c
struct wild_map_info_type wild_map_info[] = {
    {SECT_FIELD, "\tg\t[u65292],\tn", {...}},      // Green comma
    {SECT_FOREST, "\tG\t[u127795/Y]\tn", {...}},   // Green tree
    {SECT_MOUNTAIN, "\tw\t[u127956/^]\tn", {...}}, // White caret
    // ... more terrain types
};
```

## Room Management

### Static vs Dynamic Rooms

**Static Rooms (1000000-1003999)**
- Pre-built by builders using OLC
- Permanent locations (cities, dungeons, landmarks)
- Indexed in KD-Tree for fast lookup
- Survive server reboots

**Dynamic Rooms (1004000-1009999)**
- Created on-demand as players explore
- Temporary - recycled when not in use
- Generated using terrain algorithms
- Flagged with `ROOM_OCCUPIED` when active

### Room Lookup Process

```c
room_rnum find_room_by_coordinates(int x, int y) {
    // 1. Check static rooms first (KD-Tree lookup)
    room = find_static_room_by_coordinates(x, y);
    if (room != NOWHERE) return room;
    
    // 2. Check dynamic room pool
    for (dynamic rooms) {
        if (coordinates match && ROOM_OCCUPIED) 
            return room;
    }
    
    // 3. Allocate new dynamic room if needed
    return find_available_wilderness_room();
}
```

### Room Assignment

When a new wilderness location is accessed:

```c
void assign_wilderness_room(room_rnum room, int x, int y) {
    // Set coordinates
    world[room].coords[0] = x;
    world[room].coords[1] = y;
    
    // Generate terrain-based properties
    sector_type = get_modified_sector_type(zone, x, y);
    world[room].sector_type = sector_type;
    
    // Apply region modifications
    regions = get_enclosing_regions(zone, x, y);
    // Apply path modifications  
    paths = get_enclosing_paths(zone, x, y);
    
    // Generate description
    world[room].description = generate_description();
}
```

## Regions and Paths

### Regions

Regions are polygonal areas that modify wilderness properties:

```c
struct region_data {
    region_vnum vnum;           // Unique identifier
    zone_rnum zone;             // Associated zone
    char *name;                 // Region name
    int region_type;            // Type of region
    int region_props;           // Properties/effects
    struct vertex *vertices;    // Polygon boundary
    int num_vertices;           // Number of vertices
};
```

**Region Types:**
- `REGION_GEOGRAPHIC` - Named areas (forests, mountains)
- `REGION_ENCOUNTER` - Special encounter zones
- `REGION_SECTOR_TRANSFORM` - Changes terrain type
- `REGION_SECTOR` - Overrides sector completely

### Paths

Paths are linear features like roads and rivers:

```c
struct path_data {
    region_vnum vnum;           // Unique identifier
    zone_rnum zone;             // Associated zone  
    char *name;                 // Path name
    int path_type;              // Type of path
    int path_props;             // Sector type override
    char *glyphs[3];            // Map display symbols
    struct vertex *vertices;    // Path route
    int num_vertices;           // Number of vertices
};
```

**Path Types:**
- `PATH_ROAD` - Paved roads
- `PATH_DIRT_ROAD` - Dirt roads
- `PATH_RIVER` - Rivers and streams

### Database Integration

Regions and paths are stored in MySQL database:
- `region_data` table - Region definitions
- `path_data` table - Path definitions  
- Spatial queries using MySQL's geometry functions
- `ST_Within()` for point-in-polygon testing

## Weather System

### Wilderness Weather

The wilderness has its own weather system separate from zone-based weather:

```c
int get_weather(int x, int y) {
    // Uses NOISE_WEATHER layer for location-specific patterns
    return weather_value_based_on_coordinates;
}
```

### Weather Integration

- Each wilderness tile has its own weather value
- Weather affects visibility and movement
- Integrated with global weather patterns
- Displayed in room descriptions and maps

### Weather Exclusion

```c
if (ZONE_FLAGGED(world[IN_ROOM(character)].zone, ZONE_WILDERNESS))
    continue; /* Wilderness weather is handled elsewhere */
```

Standard weather messages don't apply to wilderness zones.

## Player Experience

### Movement

Players move through wilderness using standard directional commands:
- `north`, `south`, `east`, `west`
- Each movement changes coordinates by ±1
- Automatic room creation/assignment
- Seamless transition between static and dynamic areas

### Mapping

**Automatic Mapping:**
```c
if (PRF_FLAGGED(ch, PRF_AUTOMAP)) {
    show_wilderness_map(ch, 21, ch->coords[0], ch->coords[1]);
}
```

**Map Display:**
- 21x21 grid centered on player
- Color-coded terrain types
- Unicode symbols for terrain
- GUI mode support with XML tags

**Map Features:**
- Line-of-sight calculations
- Terrain-appropriate symbols
- Player position indicator
- Region and path overlays

### Navigation

**Coordinate Display:**
Players can see their current coordinates and use them for navigation.

**Landmarks:**
Static rooms serve as permanent landmarks and reference points.

**Paths:**
Roads and rivers provide guided travel routes between locations.

## Builder Tools

### OLC Integration

**Wilderness Room Editing:**
```c
// Coordinates automatically set in OLC
OLC_ROOM(d)->coords[0] = x_coordinate;
OLC_ROOM(d)->coords[1] = y_coordinate;
```

**Buildwalk Support:**
- `PRF_BUILDWALK` flag enables building while walking
- Automatic coordinate calculation
- Permission checking for zone editing
- Integration with existing OLC commands

### Attaching Zones to Wilderness

**Process Overview:**
1. Create zone with `ZONE_WILDERNESS` flag
2. Build rooms with coordinate assignments
3. Set up exits to wilderness navigation room (vnum 1000000)
4. Update external documentation

**Key Requirements:**
- Zone must be flagged as `ZONE_WILDERNESS`
- Rooms must have valid coordinates set
- Exits must point to navigation room for wilderness movement
- Static rooms should be in VNUM range 1000000-1003999

**Detailed Steps:**
1. **Zone Creation**: Use `zedit` to create new zone with wilderness flag
2. **Room Building**: Use `redit` to create rooms with proper coordinates
3. **Exit Setup**: Create exits pointing to room 1000000 for wilderness movement
4. **Testing**: Use buildwalk to test movement and coordinate assignment
5. **Documentation**: Update builder documentation with new zone information

### River Generation

**Command:** `genriver <direction> <vnum> <name>`

```c
ACMD(do_genriver) {
    // Generates procedural rivers
    // Creates path_data entries
    // Follows terrain elevation
    // Adds to database
}
```

**Features:**
- Follows natural terrain flow
- Automatic meandering based on elevation
- Database integration
- Visual map representation

**Usage Example:**
```
genriver north 100011 "Silverflow River"
```

### Region and Path Management

**Database Tools:**
- Direct MySQL manipulation for complex regions
- Spatial geometry support
- Polygon and linestring definitions
- Automatic indexing for performance

**Region Creation Process:**
1. Define polygon boundaries in MySQL
2. Set region type and properties
3. Associate with appropriate zone
4. Test in-game functionality

**Path Creation Process:**
1. Define linestring route in MySQL
2. Set path type and sector override
3. Configure display glyphs
4. Test visual representation

## Technical Implementation

### Performance Optimizations

**KD-Tree Indexing:**
```c
struct kdtree *kd_wilderness_rooms;
// O(log n) lookup for static rooms
// Spatial indexing for coordinate-based queries
```

**Dynamic Room Pooling:**
- Fixed pool of 6000 dynamic rooms
- Automatic recycling of unused rooms
- `ROOM_OCCUPIED` flag management
- Memory-efficient room reuse

**Lazy Loading:**
- Rooms created only when accessed
- Terrain generated on-demand
- Database queries optimized with spatial indexes

### Memory Management

**Static Data:**
- Region and path data loaded at startup
- KD-Tree rebuilt when wilderness zones change
- Persistent coordinate storage

**Dynamic Data:**
- Room descriptions generated as needed
- Temporary room assignments
- Automatic cleanup of unused dynamic rooms

**Memory Allocation:**
```c
// Dynamic room pool allocation
for (i = WILD_DYNAMIC_ROOM_VNUM_START; i <= WILD_DYNAMIC_ROOM_VNUM_END; i++) {
    if (real_room(i) == NOWHERE) {
        // Room available for allocation
        return real_room(i);
    }
}
```

### Database Schema

**Region Data:**
```sql
CREATE TABLE region_data (
    vnum INT PRIMARY KEY,
    zone_vnum INT,
    name VARCHAR(255),
    region_type INT,
    region_polygon GEOMETRY,
    region_props INT,
    region_reset_data TEXT,
    region_reset_time INT,
    SPATIAL INDEX(region_polygon)
);
```

**Path Data:**
```sql
CREATE TABLE path_data (
    vnum INT PRIMARY KEY,
    zone_vnum INT,
    name VARCHAR(255),
    path_type INT,
    path_linestring GEOMETRY,
    path_props INT,
    SPATIAL INDEX(path_linestring)
);
```

**Spatial Queries:**
```sql
-- Find regions containing a point
SELECT * FROM region_data
WHERE ST_Within(GeomFromText('POINT(x y)'), region_polygon);

-- Find paths near a point
SELECT * FROM path_data
WHERE ST_Distance(path_linestring, GeomFromText('POINT(x y)')) < distance;
```

### Error Handling

**Coordinate Validation:**
```c
if (x < -WILD_X_SIZE/2 || x > WILD_X_SIZE/2 ||
    y < -WILD_Y_SIZE/2 || y > WILD_Y_SIZE/2) {
    log("SYSERR: Invalid wilderness coordinates (%d, %d)", x, y);
    return NOWHERE;
}
```

**Room Allocation:**
- Fallback when dynamic room pool exhausted
- Error recovery for failed room assignments
- Comprehensive error logging

**Database Error Handling:**
```c
if (mysql_query(conn, query)) {
    log("SYSERR: MySQL query failed: %s", mysql_error(conn));
    return NULL;
}
```

## Configuration

### Wilderness Constants

```c
#define WILD_X_SIZE 2048        // Wilderness width
#define WILD_Y_SIZE 2048        // Wilderness height
#define WILD_ZONE_VNUM 10000    // Default wilderness zone
```

### Noise Seeds

```c
#define NOISE_MATERIAL_PLANE_ELEV_SEED 822344
#define NOISE_MATERIAL_PLANE_MOISTURE_SEED 834
#define NOISE_MATERIAL_PLANE_ELEV_DIST_SEED 74233
#define NOISE_WEATHER_SEED 43425
```

**Customization:**
- Change seeds to generate different terrain patterns
- Modify size constants for larger/smaller wilderness
- Adjust noise parameters for different terrain characteristics

### Sector Mapping

Terrain generation can be customized by modifying sector thresholds:

```c
struct sector_limits sector_map[NUM_ROOM_SECTORS][3];
// [sector_type][elevation/moisture/temperature] = {min, max}
```

**Example Configuration:**
```c
// Mountains at high elevation
sector_map[SECT_MOUNTAIN][0] = {200, 255};  // elevation
sector_map[SECT_MOUNTAIN][1] = {0, 255};    // moisture (any)
sector_map[SECT_MOUNTAIN][2] = {0, 100};    // temperature (cold)

// Forests in moderate elevation with high moisture
sector_map[SECT_FOREST][0] = {50, 150};     // elevation
sector_map[SECT_FOREST][1] = {150, 255};    // moisture (high)
sector_map[SECT_FOREST][2] = {100, 200};    // temperature (moderate)
```

### Database Configuration

**MySQL Connection:**
- Configure connection parameters in mysql.c
- Ensure spatial extensions are enabled
- Create appropriate indexes for performance

**Required MySQL Extensions:**
- Spatial data types (GEOMETRY, POINT, POLYGON, LINESTRING)
- Spatial functions (ST_Within, ST_Distance, GeomFromText)
- Spatial indexing support

### Perlin Noise Configuration

**Noise Parameters:**
```c
// Elevation noise - creates mountain ranges
result = PerlinNoise2D(NOISE_MATERIAL_PLANE_ELEV,
                       trans_x, trans_y,
                       2.0,    // frequency
                       2.0,    // lacunarity
                       16);    // octaves

// Ridged multifractal for realistic mountains
result = 1 - (result < 0 ? -result : result);
result *= result * result;  // Attenuation
```

**Customizable Parameters:**
- Frequency: Controls terrain feature size
- Lacunarity: Controls detail level
- Octaves: Controls noise complexity
- Attenuation: Controls mountain sharpness

## Troubleshooting

### Common Issues

**1. Wilderness Movement Fails**

*Symptoms:* Players can't move in wilderness, get "You can't go that way" messages

*Solutions:*
- Check zone has `ZONE_WILDERNESS` flag set
- Verify exits point to room 1000000 (navigation room)
- Ensure coordinates are properly set in room data
- Check that wilderness zone exists and is loaded

*Debug Commands:*
```
stat room        // Check current room flags and coordinates
goto 1000000     // Test navigation room accessibility
```

**2. Terrain Not Generating Properly**

*Symptoms:* All wilderness shows same terrain type, no variation

*Solutions:*
- Verify Perlin noise initialization with correct seeds
- Check noise seed values in wilderness.h
- Confirm sector mapping configuration
- Test noise generation functions

*Debug Steps:*
```c
// Test noise generation
double elev = get_elevation(NOISE_MATERIAL_PLANE_ELEV, x, y);
double moist = get_moisture(NOISE_MATERIAL_PLANE_MOISTURE, x, y);
int sector = get_sector_type(elev, temp, moist);
```

**3. Regions/Paths Not Working**

*Symptoms:* Regions don't affect terrain, paths don't display

*Solutions:*
- Check MySQL connection and database accessibility
- Verify spatial data exists in region_data/path_data tables
- Confirm geometry functions are available in MySQL
- Test spatial queries manually

*Database Tests:*
```sql
-- Test region data
SELECT COUNT(*) FROM region_data;
SELECT * FROM region_data WHERE zone_vnum = your_zone;

-- Test spatial functions
SELECT ST_Within(GeomFromText('POINT(0 0)'), region_polygon)
FROM region_data LIMIT 1;
```

**4. Performance Issues**

*Symptoms:* Slow wilderness movement, lag when exploring

*Solutions:*
- Monitor KD-Tree performance and rebuild if needed
- Check dynamic room pool usage and increase if necessary
- Optimize database queries with proper indexes
- Profile memory usage patterns

*Performance Monitoring:*
```c
// Check dynamic room pool usage
int used_rooms = 0;
for (i = WILD_DYNAMIC_ROOM_VNUM_START; i <= WILD_DYNAMIC_ROOM_VNUM_END; i++) {
    if (real_room(i) != NOWHERE && ROOM_FLAGGED(real_room(i), ROOM_OCCUPIED))
        used_rooms++;
}
log("Dynamic rooms in use: %d/%d", used_rooms,
    WILD_DYNAMIC_ROOM_VNUM_END - WILD_DYNAMIC_ROOM_VNUM_START + 1);
```

**5. Map Display Problems**

*Symptoms:* Maps show incorrectly, missing colors/symbols

*Solutions:*
- Verify Unicode support in client
- Check color code compatibility with client
- Confirm GUI mode settings for XML tags
- Test with different terminal/client software

*Map Debug:*
```c
// Test map generation
char *map_str = gen_ascii_wilderness_map(21, x, y, MAP_TYPE_NORMAL);
send_to_char(ch, "Raw map data:\r\n%s\r\n", map_str);
```

### Debug Commands

**Wilderness Information Commands:**
```
stat room                    // Show current room info including coordinates
goto <x> <y>                // Go to specific wilderness coordinates
genmap <size> <filename>     // Generate map file for debugging
genriver <dir> <vnum> <name> // Create test river
```

**Database Debugging:**
```sql
-- Check region coverage
SELECT r.name, COUNT(*) as rooms_affected
FROM region_data r
JOIN wilderness_rooms w ON ST_Within(w.point, r.region_polygon)
GROUP BY r.vnum;

-- Verify path data
SELECT name, path_type, ST_AsText(path_linestring)
FROM path_data
WHERE zone_vnum = your_zone;
```

### Log Messages

The system provides comprehensive logging for debugging:

**Room Management:**
```
INFO: Assigning wilderness room %d to coordinates (%d, %d)
SYSERR: Attempted to assign NOWHERE as wilderness location at (%d, %d)
SYSERR: Wilderness movement failed from (%d, %d) to (%d, %d)
```

**Database Operations:**
```
INFO: Loading region data from MySQL
SYSERR: Unable to SELECT from region_data: <error>
INFO: Deleting Path [%d] from MySQL
```

**Performance Warnings:**
```
WARNING: Dynamic room pool nearly exhausted (%d/%d used)
WARNING: KD-Tree lookup taking longer than expected
```

### Performance Monitoring

**Key Metrics to Monitor:**

1. **Dynamic Room Pool Utilization**
   - Target: < 80% usage during peak times
   - Alert: > 90% usage indicates need for pool expansion

2. **KD-Tree Query Performance**
   - Target: < 1ms average lookup time
   - Alert: > 10ms indicates need for tree rebuild

3. **Database Response Times**
   - Target: < 100ms for region/path queries
   - Alert: > 500ms indicates indexing issues

4. **Memory Usage Patterns**
   - Monitor for memory leaks in dynamic room allocation
   - Check for proper cleanup of unused rooms

**Monitoring Commands:**
```c
// Add to periodic maintenance
void wilderness_performance_check() {
    // Check room pool usage
    // Monitor KD-tree performance
    // Test database connectivity
    // Report metrics to logs
}
```

### Maintenance Tasks

**Regular Maintenance:**
- Rebuild KD-Tree indexes when static rooms change
- Clean up orphaned dynamic rooms
- Optimize database indexes
- Monitor and rotate log files

**Database Maintenance:**
```sql
-- Optimize spatial indexes
OPTIMIZE TABLE region_data;
OPTIMIZE TABLE path_data;

-- Check for data integrity
SELECT COUNT(*) FROM region_data WHERE region_polygon IS NULL;
SELECT COUNT(*) FROM path_data WHERE path_linestring IS NULL;
```

---

## Additional Resources

### Related Documentation
- `documentation/building_game-data/attaching_zones_wilderness.md` - Builder's guide for zone attachment
- `documentation/systems/WORLD_SIMULATION_SYSTEM.md` - Technical system overview
- `documentation/DATA_STRUCTURES_AND_MEMORY.md` - Data structure documentation

### Source Code Files
- `wilderness.c/h` - Main wilderness implementation
- `perlin.c/h` - Noise generation algorithms
- `mysql.c/h` - Database integration
- `act.movement.c` - Movement handling
- `weather.c` - Weather system integration

### Database Schema Files
- Region and path table definitions
- Spatial index configurations
- Sample data for testing

---

*This comprehensive documentation covers all aspects of the Luminari MUD Wilderness System. For specific implementation details or advanced customization, refer to the source code and consult with the development team.*
