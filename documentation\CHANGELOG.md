# CHANGELOG

## 2025-07-28

### Fixes
  - Fix: Fixed OLC script proto list memory leak in dg_olc.c
    - Added cleanup of existing OLC_SCRIPT list at the beginning of `dg_olc_script_copy()`
    - Prevents memory leak when OLC script editing functions are called multiple times
    - The leak occurred because the function would create a new list without freeing the old one
    - Added loop to free all nodes in the existing list before creating the new copy

## 2025-07-28

### Fixes
  - Fix: **CRITICAL** - Fixed DG Scripts lookup table memory leak
    - Fixed `remove_from_lookup_table()` function that couldn't properly remove entries from static bucket heads
    - Added proper handling to copy next entry to static bucket when removing the first entry
    - Added `cleanup_lookup_table()` function to free all linked list entries on shutdown
    - Called cleanup function in comm.c shutdown routine to prevent memory leaks
    - This fixes unbounded growth of lookup table entries as NPCs/objects spawn and despawn
  - Fix: Fixed communication buffer memory leak in whisper/ask commands
    - Added missing `free(buf3)` in `do_spec_comm()` function in act.comm.do_spec_comm.c
    - The buf3 string was allocated with `strdup()` but never freed after use
    - This caused a memory leak every time players used whisper or ask commands
  - Fix: Fixed object save data chain cleanup bug
    - Fixed logic error in `objsave_parse_objects()` where cleanup loop would exit prematurely
    - The loop was setting `tempsave->next = NULL` then trying to use it as the iterator
    - This prevented proper cleanup of empty nodes at end of object save chains

## 2025-07-28

### Fixes
  - Fix: Fixed memory leak in corpse creation system
    - Added missing free() for char_sdesc field in free_object_strings() and free_object_strings_proto() in genobj.c
    - Corpses store a strdup'd copy of the character's short description but it wasn't being freed when the corpse was destroyed
    - This caused a memory leak every time a corpse was created and later destroyed
  - Fix: Fixed memory leaks in character description functions (char_descs.c)
    - Changed current_morphed_desc(), current_wildshape_desc(), and current_disguise_desc() to use static buffers instead of strdup()
    - Changed show_pers() to return a static buffer for "someone" instead of strdup()
    - These functions were returning newly allocated strings that callers weren't freeing
  - Fix: Fixed memory leak in template system (templates.c)
    - Removed unnecessary strdup() calls when passing strings to levelinfo_search()
    - The levelinfo_search() function doesn't free its searchString parameter, so passing strdup'd strings caused leaks

## 2025-07-28

### Fixes
  - Fix: Fixed memory leaks in event system variable string reassignment
    - Added missing free() call in act.informative.c:8308 before reassigning pMudEvent->sVariables
    - This prevents memory leak during track aging events (6 bytes leaked every 60 seconds per track)
  - Fix: Fixed memory leaks in crafting node creation
    - Added free() calls before overwriting object name/description strings in craft.c:775-780
    - When creating harvesting nodes, the prototype object's strings were being overwritten without freeing
    - This caused memory leaks each time harvesting nodes were created in the world

## 2025-07-28

### Fixes
  - Fix: Fixed file handle leak in `list_llog_entries()` in act.wizard.c
    - Added missing return statement at line 2628 when file open fails
    - Added missing fclose() at line 2639 to close file handle after reading
    - Prevents file descriptor exhaustion when command is repeatedly used

## 2025-07-28

### Fixes
  - Fix: Fixed memory leaks in critical paths identified by valgrind
    - Fixed file descriptor leak in `find_llog_entry()` in act.wizard.c:2453 where file was not closed on read error
    - Fixed character memory leak in `do_show()` in act.wizard.c:2766 where vict was not freed when permission check failed
    - Note: Other issues in valgrind_todo.md were already fixed (tokenize, new_descriptor, setup_dir)

## 2025-07-28

### Fixes
  - Fix: Fixed AI service memory leak on shutdown
    - Added missing `shutdown_ai_service()` call in main cleanup routine in comm.c
    - This properly frees all AI cache entries, configuration, rate limiter, and CURL handles
    - Prevents memory leaks from accumulated AI service data during game operation
    - Added #include "ai_service.h" to comm.c for the shutdown function

## 2025-07-28

### Fixes
  - Fix: Fixed multiple memory leaks from string reassignments without freeing
    - Added free() calls before room name/description assignments in `do_setroomname()` and `do_setroomdesc()` in act.wizard.c
    - Added free() calls before room name/description assignments in travel system in act.informative.c (lines 1285,1292,1296,1303,1307,1322,1326)
    - Prevents memory leaks when rooms are renamed or travel descriptions are updated
  - Fix: Fixed temporary string allocation memory leaks
    - Fixed strdup() memory leak in act.wizard.c:3376 for zone builders list parsing
    - Fixed strdup() memory leaks in act.comm.c for speech trigger processing (lines 59,158,234)
    - Added proper free() calls after trigger processing to prevent 6+ bytes leaked per say/ask/tell command
  - Fix: Fixed forge signature memory leak in backgrounds.c
    - Added free() call before overwriting forge_as_signature in `do_forge_as()` at line 826
    - Prevents memory leak when players repeatedly use the forge command

## 2025-07-28

### Fixes
  - Fix: Fixed memory leaks in event system from redundant strdup() calls
    - `new_mud_event()` already duplicates the string parameter internally
    - Removed redundant `strdup()` calls in spell_prep.c:2576, bardic_performance.c:399
    - Removed redundant `strdup()` calls in crafts.c:727, act.wizard.c:6251, traps.c:57
    - Prevents 6+ bytes leaked per spell preparation, bardic performance, crafting, etc.
    - These leaks accumulated over time as players used these systems frequently
  - Fix: Fixed memory leaks in account loading system
    - Activated previously commented cleanup code in `load_account()` function
    - Now properly frees existing account->name and account->email before allocating new memory
    - Also frees character_names array to prevent leaks when account is reloaded
    - Prevents 2-7 bytes leaked per login when accounts are loaded multiple times
    - Addresses valgrind memory leaks at account.c:401 and account.c:405

## 2025-07-28

### Fixes
  - Fix: **CRITICAL** - Fixed invalid memory access in spell casting system
    - Added validation in `castingCheckOk()` to verify target character still exists before accessing
    - Prevents crashes when spell target is extracted during casting (e.g., death, disconnect)
    - Traverses character_list to ensure CASTING_TCH pointer is still valid
    - Addresses valgrind error: "Invalid read of size 4" at spell_parser.c:1405
  - Fix: **CRITICAL** - Fixed uninitialized values in player save system
    - Added initialization of all player_table fields in `create_entry()` function
    - Prevents conditional jumps based on uninitialized data when saving characters
    - Initializes id, level, and last fields to 0 to prevent save file corruption
    - Addresses valgrind errors at players.c:2920 and players.c:2925
  - Fix: **CRITICAL** - Fixed uninitialized values in player index file operations
    - Added initialization loop in `build_player_index()` to set all fields to safe defaults
    - Added NULL check for player names in `save_player_index()` before dereferencing
    - Prevents fprintf from accessing uninitialized memory when writing player index
    - Addresses multiple valgrind errors at players.c:275 during index file writes
  - Fix: **CRITICAL** - Fixed metamagic spell stacking exploit in casting system
    - Added pre-cast validation in `do_gen_cast()` to verify exact spell+metamagic combination
    - Players can no longer cast metamagic spells using non-metamagic spell slots
    - Added exploit detection logging for attempts to bypass metamagic costs
    - Added debug logging in `spell_prep_gen_extract()` for metamagic spell tracking
    - Improved error messages when attempting to cast unprepared metamagic spells
  - Fix: Added explicit initialization loop for all queue head/tail pointers
  - Fix: Added `free_action_queue()` and `free_attack_queue()` calls to `free_mobile()`
  - Fix: 
    - Fixed memory leaks in track pruning code in `create_tracks()`
    - Added missing `free()` for trail_data_list structure in `free_trail_data_list()`
    - Added periodic `cleanup_all_trails()` function called once per mud hour
  - Fix:
    - Added proper NULL checks after all `strdup()` calls
    - Fixed error paths to properly free allocated memory before returning
    - Ensured CURL handles are only cleaned up when not using persistent handle
  - Fix: Removed redundant `strdup()` calls when passing strings to `new_mud_event()`
    - Changed `new_mud_event(eCOMBAT_ROUND, ch, strdup("1"))` to `new_mud_event(eCOMBAT_ROUND, ch, "1")`
    - Changed `new_mud_event(eCRIPPLING_CRITICAL, victim, strdup("1"))` to `new_mud_event(eCRIPPLING_CRITICAL, victim, "1")`
  - Fix: Moved action queue cleanup outside of player_specials check in `free_char()`
  - Fix: Fixed MySQL result memory leak in `load_account()`
    - Added missing `mysql_free_result()` call when account is not found in `account.c:394`
  - Fix: Fixed critical uninitialized memory access in DG Event Queue system
    - Added NULL queue safety checks to all queue operations (`queue_enq`, `queue_deq`, `queue_head`, `queue_key`)
    - Added validation in `event_create()` and `event_process()` to ensure event_q is initialized before use
    - Prevents potential crashes from accessing uninitialized queue pointers
  - Fix: Fixed memory leaks in Fight System during combat
    - Added cleanup of combat-related events (`eSMASH_DEFENSE`, `eSTUNNED`) in `stop_fighting()`
    - Fixed duplicate event creation by checking if `eSMASH_DEFENSE` event already exists before creating
    - Prevents memory exhaustion during prolonged combat sessions
  - Fix: Fixed string duplication memory leak in `do_put()` command
    - Added proper `free()` calls for `thecont` and `theobj` strings allocated with `strdup()`
    - Memory leak occurred every time items were put in containers
    - Fixed by freeing strings before all return paths and at function exit
  - Fix: Fixed character save memory leak in `save_char()` function
    - Added `free()` call before overwriting existing character name in account data
    - Memory leak occurred every time a character saved (6 bytes per save)
    - Fixed by freeing old string before assigning new one with `strdup()`
  - Fix: Fixed account character loading issues in `account.c`
    - Added bounds check to prevent buffer overflow when loading more than MAX_CHARS_PER_ACCOUNT characters
    - Fixed potential memory leak in `get_char_account_name()` when multiple rows are returned
    - Prevents crashes and memory corruption during login
  - Fix: Fixed crafting system memory leaks in character loading
    - Added `free()` calls before overwriting craft description strings when loading character data
    - Memory leak occurred because `reset_current_craft()` allocates default strings that were overwritten without freeing
    - Fixed in `players.c` for keywords, short_description, room_description, and ex_description fields
  - Fix: Fixed uninitialized value errors in DG Scripts `process_wait()` function
    - Variables `when`, `hr`, `min`, `ntime`, and `c` were not initialized before use
    - Issue occurred when sscanf failed to parse the "wait until" time format
    - Added proper initialization of all local variables to 0
    - Added missing sscanf check for alternate time format (1430 vs 14:30)
    - Added error handling and logging for invalid time formats in `dg_scripts.c:1851`
  - Fix: Fixed track system memory leak on game shutdown
    - Added `free_trail_data_list()` call in `destroy_db()` to properly free trail data when rooms are destroyed
    - This fixes the accumulation of trail data (6 bytes per movement) that was not being freed at shutdown
    - Added function declaration to `genwld.h` and included header in `db.c`
    - Addresses valgrind memory leak at act.movement.c:615-616

