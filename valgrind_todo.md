# Valgrind Memory Issues TODO List

## Critical Memory Leak Patterns Found

### 1. Unbounded Linked List Growth

#### Script Proto Lists (dg_olc.c) - FIXED
- **Issue**: `CREATE(editscript->next, struct trig_proto_list, 1)`
- **Location**: dg_olc.c - script prototype copying
- **Fix**: Added cleanup of existing OLC_SCRIPT list before copying new one
- **Impact**: LOW - Only during OLC editing
- **Status**: FIXED - Added free loop at beginning of dg_olc_script_copy()

### 2. Event System Memory Leaks

#### Event Queue Growth
- **Issue**: Events created but not always properly cancelled
- **Pattern**: `NEW_EVENT()` and `event_create()` calls without matching cleanup
- **Locations**:
  - comm.c: Protocol detection events
  - bardic_performance.c: Performance events
  - ai_events.c: AI response events
  - act.wizard.c: Copyover events
- **Fix**: Audit all event creation to ensure proper cancellation on character extraction
- **Impact**: HIGH - Events accumulate indefinitely

#### Character Event Lists
- **Issue**: `clear_char_event_list()` called but individual events may leak
- **Location**: act.wizard.c (SCMD_UNAFFECT)
- **Fix**: Verify event cleanup actually frees all memory
- **Impact**: MEDIUM

### 3. String Duplication Without Cleanup

#### AI Cache Strings (ai_cache.c)
- **Issue**: `strdup()` used for keys and responses
- **Location**: ai_cache.c lines 91, 111, 126
- **Fix**: Already has cleanup, but verify it's called on shutdown
- **Impact**: LOW - Has proper cleanup mechanisms

### 4. Global List Management Issues

#### Character List
- **Issue**: `character_list` global may retain references to freed characters
- **Location**: db.c - `struct char_data *character_list`
- **Fix**: Audit character extraction to ensure complete removal
- **Impact**: CRITICAL - Can cause crashes and memory corruption

#### Object List
- **Issue**: `object_list` global may retain references to freed objects
- **Location**: db.c - `struct obj_data *object_list`
- **Fix**: Audit object extraction to ensure complete removal
- **Impact**: CRITICAL - Can cause crashes and memory corruption

#### Descriptor List
- **Issue**: `REMOVE_FROM_LIST(d, descriptor_list, next)` patterns need verification
- **Location**: comm.c
- **Fix**: Ensure all descriptor cleanup is complete
- **Impact**: HIGH - Network connection leaks

### 5. Zone/Room Array Growth

#### World Array Access
- **Issue**: Direct world array access like `world[cnt].sector_type` without bounds checking
- **Location**: craft.c and many other files
- **Fix**: Add bounds checking macros
- **Impact**: LOW - Array is pre-allocated but could overflow

### 6. Cache Systems Without Expiration

#### AI Cache
- **Status**: GOOD - Has proper expiration and cleanup
- **Location**: ai_cache.c
- **Note**: Verified `ai_cache_cleanup()` is called periodically and shutdown_ai_service() is called on game shutdown

### 7. Buffer Allocations

#### Stack Buffers
- **Issue**: Large stack allocations like `char buf[MAX_STRING_LENGTH]`
- **Locations**: Throughout codebase
- **Fix**: Consider heap allocation for large buffers
- **Impact**: LOW - Stack overflow risk

#### CREATE Macro Memory
- **Issue**: `CREATE()` allocations without matching cleanup
- **Pattern**: Search for all CREATE calls and verify cleanup paths
- **Impact**: VARIES

### 8. Specific Function Audits Needed

#### High Priority Functions to Audit:
1. `free_char()` - Ensure complete character cleanup
2. `extract_obj()` - Ensure complete object cleanup  
3. `extract_char()` - Verify removal from all lists
4. `cleanup_olc()` - Check for OLC memory leaks
5. `free_proto_script()` - Verify script cleanup

#### Memory Allocation Functions to Track:
1. All `CREATE()` calls
2. All `malloc()` calls
3. All `calloc()` calls
4. All `strdup()` calls
5. All `RECREATE()` calls

### 9. Thread Safety Issues

#### AI Service Threading
- **Issue**: Potential race conditions in ai_events.c
- **Location**: Worker thread to main thread communication
- **Fix**: Add proper synchronization
- **Impact**: MEDIUM - Could cause memory corruption

### 10. Recommended Valgrind Commands

```bash
# Full leak check
valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes ../bin/circle

# With suppression file for known issues
valgrind --leak-check=full --suppressions=valgrind.supp ../bin/circle

# Memory error detection focus
valgrind --tool=memcheck --track-origins=yes ../bin/circle

# Heap profiling
valgrind --tool=massif ../bin/circle
```

## Implementation Priority

1. **CRITICAL**: No critical issues remaining
2. **HIGH**: Monitor event system for any missed cleanup paths
3. **MEDIUM**: Fix remaining string duplication leaks
4. **LOW**: Add bounds checking and optimize buffer usage

## Fixed Issues (2025-07-28)

1. **Script Proto Lists Memory Leak** - Fixed by adding cleanup in dg_olc_script_copy()
2. **AI Service Shutdown** - Already properly handled in comm.c shutdown
3. **Character/Object List Integrity** - Verified proper removal from lists on extraction
4. **Event System Cleanup** - Verified proper cleanup in character extraction and descriptor close

## Testing Strategy

1. Create test cases that spawn/despawn many NPCs rapidly
2. Test OLC operations repeatedly
3. Test player login/logout cycles
4. Monitor memory usage over 24-48 hour periods
5. Use automated testing to trigger all code paths

## Notes

- Many of these issues accumulate slowly over days/weeks
- Focus on the most frequently called code paths first
- Consider implementing a memory pool system for frequently allocated structures
- Add memory usage statistics commands for monitoring